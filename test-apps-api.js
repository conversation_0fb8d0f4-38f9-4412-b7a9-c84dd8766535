// Test script for Applications API endpoints
// Run with: node test-apps-api.js

const BASE_URL = 'http://localhost:3000/api';

async function testApplicationsAPI() {
  console.log('🚀 Testing Applications Management API\n');

  // Test 1: Create a new application
  console.log('1. Creating a new application...');
  try {
    const createResponse = await fetch(`${BASE_URL}/apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token'
      },
      body: JSON.stringify({
        name: 'Test Application',
        description: 'A test application for demonstration',
        redirectUris: [
          'https://example.com/callback',
          'https://localhost:3000/callback'
        ],
        scopes: ['read', 'write', 'credentials']
      })
    });

    const createData = await createResponse.json();
    console.log('✅ Create Application Response:', JSON.stringify(createData, null, 2));
    
    if (createData.success && createData.data) {
      const { clientId, clientSecret } = createData.data;
      
      // Test 2: Get all applications
      console.log('\n2. Fetching all applications...');
      const getResponse = await fetch(`${BASE_URL}/apps`, {
        headers: {
          'Authorization': 'Bearer mock-token'
        }
      });
      
      const getData = await getResponse.json();
      console.log('✅ Get Applications Response:', JSON.stringify(getData, null, 2));
      
      // Test 3: Verify application credentials
      console.log('\n3. Verifying application credentials...');
      const verifyResponse = await fetch(`${BASE_URL}/apps/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId,
          clientSecret
        })
      });
      
      const verifyData = await verifyResponse.json();
      console.log('✅ Verify Application Response:', JSON.stringify(verifyData, null, 2));
      
      // Test 4: Verify with invalid credentials
      console.log('\n4. Testing with invalid credentials...');
      const invalidVerifyResponse = await fetch(`${BASE_URL}/apps/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: 'invalid_client_id',
          clientSecret: 'invalid_client_secret'
        })
      });
      
      const invalidVerifyData = await invalidVerifyResponse.json();
      console.log('❌ Invalid Credentials Response:', JSON.stringify(invalidVerifyData, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
  
  console.log('\n🎉 API testing completed!');
}

// Test validation errors
async function testValidationErrors() {
  console.log('\n🔍 Testing validation errors...\n');
  
  // Test missing required fields
  console.log('1. Testing missing required fields...');
  try {
    const response = await fetch(`${BASE_URL}/apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token'
      },
      body: JSON.stringify({
        name: '', // Empty name
        redirectUris: [], // Empty redirect URIs
        scopes: [] // Empty scopes
      })
    });
    
    const data = await response.json();
    console.log('✅ Validation Error Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  
  // Test invalid redirect URI
  console.log('\n2. Testing invalid redirect URI...');
  try {
    const response = await fetch(`${BASE_URL}/apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token'
      },
      body: JSON.stringify({
        name: 'Test App',
        redirectUris: ['not-a-valid-url'], // Invalid URL
        scopes: ['read']
      })
    });
    
    const data = await response.json();
    console.log('✅ Invalid URI Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Test unauthorized access
async function testUnauthorizedAccess() {
  console.log('\n🔒 Testing unauthorized access...\n');
  
  try {
    const response = await fetch(`${BASE_URL}/apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // No Authorization header
      },
      body: JSON.stringify({
        name: 'Test App',
        redirectUris: ['https://example.com'],
        scopes: ['read']
      })
    });
    
    const data = await response.json();
    console.log('✅ Unauthorized Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await testApplicationsAPI();
  await testValidationErrors();
  await testUnauthorizedAccess();
}

// Check if running directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testApplicationsAPI,
  testValidationErrors,
  testUnauthorizedAccess,
  runAllTests
};