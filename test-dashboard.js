#!/usr/bin/env node

/**
 * Simple test script to verify the developer dashboard functionality
 * Run with: node test-dashboard.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Developer Dashboard Implementation...\n');

// Test 1: Check if all required files exist
const requiredFiles = [
  'app/dashboard/dashboard/page.tsx',
  'lib/api.ts',
  'app/api/apps/route.ts',
  'app/api/apps/verify/route.ts',
  'app/api/apps/[appId]/route.ts',
  'components/shared/dashboard/sidebar.tsx',
  'DEVELOPER_DASHBOARD_GUIDE.md'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing!');
  process.exit(1);
}

// Test 2: Check if package.json has required dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
const requiredDeps = [
  'react',
  'next',
  'sonner',
  'lucide-react',
  '@radix-ui/react-dialog',
  '@radix-ui/react-dropdown-menu'
];

let allDepsPresent = true;
requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies?.[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
    allDepsPresent = false;
  }
});

// Test 3: Check API service implementation
console.log('\n🔌 Checking API service...');
const apiContent = fs.readFileSync(path.join(__dirname, 'lib/api.ts'), 'utf8');
const apiMethods = [
  'createApplication',
  'getApplications',
  'verifyApplication',
  'deleteApplication',
  'updateApplication'
];

let allMethodsPresent = true;
apiMethods.forEach(method => {
  if (apiContent.includes(method)) {
    console.log(`✅ ${method}`);
  } else {
    console.log(`❌ ${method} - MISSING`);
    allMethodsPresent = false;
  }
});

// Test 4: Check dashboard page implementation
console.log('\n🎨 Checking dashboard features...');
const dashboardContent = fs.readFileSync(path.join(__dirname, 'app/dashboard/dashboard/page.tsx'), 'utf8');
const dashboardFeatures = [
  'handleCreateApplication',
  'handleDeleteApplication',
  'handleEditApplication',
  'handleVerifyApplication',
  'DropdownMenu',
  'Dialog',
  'toast'
];

let allFeaturesPresent = true;
dashboardFeatures.forEach(feature => {
  if (dashboardContent.includes(feature)) {
    console.log(`✅ ${feature}`);
  } else {
    console.log(`❌ ${feature} - MISSING`);
    allFeaturesPresent = false;
  }
});

// Test 5: Check API routes
console.log('\n🛣️  Checking API routes...');
const routes = [
  { file: 'app/api/apps/route.ts', methods: ['GET', 'POST'] },
  { file: 'app/api/apps/verify/route.ts', methods: ['POST'] },
  { file: 'app/api/apps/[appId]/route.ts', methods: ['PUT', 'DELETE'] }
];

let allRoutesValid = true;
routes.forEach(route => {
  const routeContent = fs.readFileSync(path.join(__dirname, route.file), 'utf8');
  route.methods.forEach(method => {
    if (routeContent.includes(`export async function ${method}`)) {
      console.log(`✅ ${route.file} - ${method}`);
    } else {
      console.log(`❌ ${route.file} - ${method} - MISSING`);
      allRoutesValid = false;
    }
  });
});

// Final result
console.log('\n' + '='.repeat(50));
if (allFilesExist && allDepsPresent && allMethodsPresent && allFeaturesPresent && allRoutesValid) {
  console.log('🎉 All tests passed! Developer Dashboard is ready to use.');
  console.log('\n��� Next steps:');
  console.log('1. Run: npm run dev');
  console.log('2. Navigate to: http://localhost:3000/dashboard/dashboard');
  console.log('3. Create your first application');
  console.log('4. Test all features (create, edit, delete, verify)');
  console.log('\n📖 Read DEVELOPER_DASHBOARD_GUIDE.md for detailed documentation');
} else {
  console.log('❌ Some tests failed. Please check the issues above.');
  process.exit(1);
}

console.log('='.repeat(50));