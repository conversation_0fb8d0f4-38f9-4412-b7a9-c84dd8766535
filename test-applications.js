// Test script for Applications Management API
// Run this with: node test-applications.js (after starting the dev server)

const BASE_URL = 'http://localhost:3000/api';

// Mock JWT token for testing
const MOCK_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

async function testCreateApplication() {
  console.log('🧪 Testing Create Application...');
  
  try {
    const response = await fetch(`${BASE_URL}/apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${MOCK_TOKEN}`
      },
      body: JSON.stringify({
        name: 'Test Application',
        allowedDomains: ['https://example.com', 'https://test.com']
      })
    });

    const data = await response.json();
    console.log('✅ Create Application Response:', data);
    return data;
  } catch (error) {
    console.error('❌ Create Application Error:', error);
  }
}

async function testGetApplications() {
  console.log('🧪 Testing Get Applications...');
  
  try {
    const response = await fetch(`${BASE_URL}/apps`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${MOCK_TOKEN}`
      }
    });

    const data = await response.json();
    console.log('✅ Get Applications Response:', data);
    return data;
  } catch (error) {
    console.error('❌ Get Applications Error:', error);
  }
}

async function testVerifyApplication() {
  console.log('🧪 Testing Verify Application...');
  
  try {
    const response = await fetch(`${BASE_URL}/apps/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${MOCK_TOKEN}`
      },
      body: JSON.stringify({
        appId: 'app_5f8d04a7b4b3c9001f3e5a9b',
        apiKey: 'ak_5f8d04a7b4b3c9001f3e5a9b'
      })
    });

    const data = await response.json();
    console.log('✅ Verify Application Response:', data);
    return data;
  } catch (error) {
    console.error('❌ Verify Application Error:', error);
  }
}

async function runTests() {
  console.log('🚀 Starting Applications API Tests...\n');
  
  // Test all endpoints
  await testCreateApplication();
  console.log('');
  
  await testGetApplications();
  console.log('');
  
  await testVerifyApplication();
  console.log('');
  
  console.log('✨ Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testCreateApplication,
  testGetApplications,
  testVerifyApplication
};