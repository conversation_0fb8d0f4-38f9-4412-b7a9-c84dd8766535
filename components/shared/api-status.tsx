"use client";

import { useState, useEffect } from "react";
import { checkApiHealth, logApiHealthStatus, type HealthCheckResult } from "@/lib/api-health";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw, AlertCircle, CheckCircle, Clock } from "lucide-react";

export function ApiStatus() {
  const [healthStatus, setHealthStatus] = useState<HealthCheckResult | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const performHealthCheck = async () => {
    setIsChecking(true);
    try {
      const result = await checkApiHealth();
      setHealthStatus(result);
      logApiHealthStatus(result);
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Perform initial health check
    performHealthCheck();
  }, []);

  if (!healthStatus && !isChecking) {
    return null;
  }

  const getStatusIcon = () => {
    if (isChecking) return <RefreshCw className="h-4 w-4 animate-spin" />;
    if (healthStatus?.isHealthy) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <AlertCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = () => {
    if (isChecking) return <Badge variant="secondary">Checking...</Badge>;
    if (healthStatus?.isHealthy) return <Badge variant="default" className="bg-green-500">Healthy</Badge>;
    return <Badge variant="destructive">Unhealthy</Badge>;
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          {getStatusIcon()}
          API Status
        </CardTitle>
        <div className="flex items-center gap-2">
          {getStatusBadge()}
          <Button
            variant="outline"
            size="sm"
            onClick={performHealthCheck}
            disabled={isChecking}
            className="h-6 w-6 p-0"
          >
            <RefreshCw className={`h-3 w-3 ${isChecking ? "animate-spin" : ""}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {healthStatus && (
          <>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Status:</span>
              <span className={healthStatus.isHealthy ? "text-green-600" : "text-red-600"}>
                {healthStatus.status || 'N/A'}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Response Time:</span>
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {healthStatus.responseTime}ms
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              Last checked: {new Date(healthStatus.timestamp).toLocaleTimeString()}
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="w-full text-xs"
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
            
            {showDetails && (
              <div className="mt-2 p-2 bg-muted rounded text-xs">
                <div className="font-medium mb-1">Message:</div>
                <div className="text-muted-foreground break-words">
                  {healthStatus.message}
                </div>
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-2">
                    <div className="font-medium mb-1">Environment:</div>
                    <div className="text-muted-foreground">
                      API URL: {process.env.NEXT_PUBLIC_API_URL}
                    </div>
                    <div className="text-muted-foreground">
                      Mock API: {process.env.NEXT_PUBLIC_USE_MOCK_API || 'false'}
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}