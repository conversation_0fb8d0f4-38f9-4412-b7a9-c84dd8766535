'use client';

import { WalletConnect, useWalletConnection } from '@/components/shared/client-wallet-connect';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function WalletConnectDemo() {
  const { address, isConnected } = useWalletConnection();

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Wallet Connection Demo</h1>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>RainbowKit Wallet Connect</CardTitle>
            <CardDescription>
              Multi-wallet connection experience with RainbowKit
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <WalletConnect />
          </CardContent>
        </Card>
      </div>

      {isConnected && address && (
        <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg max-w-2xl mx-auto">
          <p className="text-center text-green-800">
            Connected with address: <code className="font-mono">{address.slice(0, 6)}...{address.slice(-4)}</code>
          </p>
        </div>
      )}
    </div>
  );
}
