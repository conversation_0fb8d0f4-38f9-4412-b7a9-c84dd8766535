'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Github, Twitter, Linkedin, Mail } from 'lucide-react';

const Footer = ({ className = "" }) => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    { icon: Github, href: "https://github.com", label: "GitHub" },
    { icon: Twitter, href: "https://twitter.com", label: "Twitter" },
    { icon: Linkedin, href: "https://linkedin.com", label: "LinkedIn" },
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
  ];

  const footerLinks = [
    { href: "/docs", label: "Documentation" },
    { href: "/#features", label: "Features" },
    { href: "/about", label: "About" },
    { href: "/privacy", label: "Privacy" },
    { href: "/terms", label: "Terms" },
  ];

  return (
    <footer className={`relative bg-transparent px-4 md:px-6 py-6 ${className}`}>
      {/* Simple gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-purple-50/30 opacity-50"></div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="flex flex-col items-center mb-4">
          <Link 
            href="/landing" 
            className="text-xl font-heading font-bold text-purple-900 inline-block"
          >
            Crefy Connect
          </Link>
          <div className="h-1 w-10 bg-gradient-to-r from-purple-900 to-purple-700 mx-auto mt-2 mb-2 rounded-full"></div>
        </div>

        <div className="flex flex-wrap justify-center gap-4 mb-4">
          {footerLinks.map((link) => (
            <Link
              key={link.label}
              href={link.href}
              className="text-xs text-slate-600 hover:text-purple-800 transition-colors"
            >
              {link.label}
            </Link>
          ))}
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center border-t border-purple-100 pt-4">
          <motion.p 
            className="text-xs text-slate-500 mb-3 sm:mb-0"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            &copy; {currentYear} Crefy Connect. All rights reserved.
          </motion.p>
          
          <motion.div 
            className="flex space-x-3"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            {socialLinks.map(social => (
              <a
                key={social.label}
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={social.label}
                className="text-slate-500 hover:text-purple-800 transition-colors duration-300"
              >
                <social.icon size={16} />
              </a>
            ))}
          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;