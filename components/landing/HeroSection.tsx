'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import DecryptedText from '@/components/ui/decrypt'; // Adjust path as needed

const HeroSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  } as const;

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  } as const;

  const textLayer1Variants = {
    hidden: { y: 30, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.6, ease: "easeOut" } },
  } as const;
  const textLayer2Variants = {
    hidden: { y: 50, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.7, ease: "easeOut", delay: 0.2 } },
  } as const;

  return (
    <section
      id="home"
      className="h-screen min-h-[650px] flex items-center justify-center relative px-4 md:px-12 py-16 pt-24 md:pt-16 bg-transparent from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800"
    >
      <motion.div
        className="max-w-3xl mx-auto text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Left Column - Text */}
        <div className="space-y-8">
          <motion.h1
            className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold leading-tight tracking-tight text-black-800 dark:text-black-200"
            variants={textLayer1Variants}
          >
              Build Smarter. Connect Faster.
          </motion.h1>
          <motion.p
            className="text-lg md:text-xl text-black-600 dark:text-black-300 max-w-xl mx-auto"
            variants={textLayer2Variants}
          >
            Wallet integration, credential management, and secure API access made effortless.
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row gap-4 mt-8 justify-center"
            variants={itemVariants}
          >
            <Link href="/auth/signup" className="px-8 py-3 block font-semibold rounded-lg text-white bg-gradient-to-r from-purple-700 to-indigo-700 hover:opacity-90 transition-opacity duration-300 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
              Get Started
            </Link>
            <Link href="#docs" className="px-8 py-3 block font-semibold rounded-lg text-purple-700 border-2 border-purple-700 bg-transparent hover:bg-purple-700 hover:text-white transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-700 focus:ring-offset-2">
              View Docs
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default HeroSection;