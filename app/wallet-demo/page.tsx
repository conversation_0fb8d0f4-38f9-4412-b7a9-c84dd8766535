'use client';

import WalletConnectDemo from '@/components/demo/wallet-connect-demo';
import { Card } from '@/components/ui/card';

export default function WalletOptionsDemo() {
  return (
    <div className="container mx-auto py-12 px-4">
      <Card className="p-6">
        <h1 className="text-3xl font-bold mb-4">Wallet Connection Options</h1>
        <p className="text-muted-foreground mb-8">
          This demo shows the different wallet connection options available in the application.
          Users can choose their preferred method to connect their wallets.
        </p>
        
        <WalletConnectDemo />
      </Card>
    </div>
  );
}
