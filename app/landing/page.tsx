'use client';

import Navbar from '@/components/landing/Navbar';
import HeroSection from '@/components/landing/HeroSection';
import DeveloperCodeSection from '@/components/landing/DeveloperCodeSection';
import TimelineSection from '@/components/landing/TimelineSection';
import ResourcesSection from '@/components/landing/ResourcesSection';
import CTASection from '@/components/landing/CTASection';
import Footer from '@/components/landing/Footer';
import Image from 'next/image';

export default function LandingPage() {
  return (
    <main className="min-h-screen text-brand-indigo font-body relative overflow-x-hidden flex flex-col">
      {/* Background image with overlay */}
      <div className="fixed inset-0 z-0">
        <Image
          src="/images/main1.jpg"
          alt="Background"
          fill
          priority
          className="object-cover"
        />
      </div>
      
      {/* Content positioned above the background */}
      <div className="flex-grow z-10 relative">
        <Navbar />
        <HeroSection />
        <DeveloperCodeSection />
        <TimelineSection />
        <ResourcesSection />
        <CTASection />
      </div>

      <Footer className="z-10 relative" />
    </main>
  );
}