import { NextRequest, NextResponse } from 'next/server';
import { apiService } from '@/lib/api';

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Validate required fields
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }

    const { appId, apiKey } = body;

    if (!appId || !apiKey) {
      return NextResponse.json({
        error: 'App ID and API key are required',
      }, { status: 422 });
    }

    // Use the API service to verify the application
    const result = await apiService.verifyApplication({ appId, apiKey });
    
    if (result.success) {
      return NextResponse.json(result.data, { status: 200 });
    } else {
      // Determine appropriate status code based on error
      let statusCode = 500;
      
      if (result.error?.includes('Unauthorized') || 
          result.error?.includes('Invalid app credentials') ||
          result.error?.includes('Invalid credentials')) {
        statusCode = 401;
      } else if (result.error?.includes('App ID and API key are required') ||
                 result.error?.includes('required')) {
        statusCode = 422;
      } else if (result.error?.includes('not found')) {
        statusCode = 404;
      }
      
      return NextResponse.json({
        error: result.error,
      }, { status: statusCode });
    }
    
  } catch (error) {
    console.error('Error during app verification:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}