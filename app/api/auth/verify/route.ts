import { NextRequest, NextResponse } from 'next/server';
import { apiService } from '@/lib/api';

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }
    
    // Use the real backend API service
    const result = await apiService.verify(body);
    
    if (result.success) {
      return NextResponse.json(result.data, { status: 200 });
    } else {
      // Determine appropriate status code based on error
      let statusCode = 500;
      if (result.error?.includes('User not found') || 
          result.error?.includes('not found')) {
        statusCode = 404;
      } else if (result.error?.includes('Invalid OTP') || 
                 result.error?.includes('OTP')) {
        statusCode = 400;
      } else if (result.error?.includes('Validation error') || 
                 result.error?.includes('Invalid email format') ||
                 result.error?.includes('OTP must be 6 digits')) {
        statusCode = 422;
      }
      
      return NextResponse.json({
        error: result.error,
      }, { status: statusCode });
    }
    
  } catch (error) {
    console.error('Error during verification:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}