import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { apiService } from '@/lib/api';

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        error: 'Invalid JSON in request body',
      }, { status: 400 });
    }
    
    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        error: 'Request body is required',
      }, { status: 400 });
    }
    
    // Validate request body
    const validatedData = loginSchema.parse(body);
    
    // Use the API service to authenticate user
    const result = await apiService.login({
      email: validatedData.email,
      password: validatedData.password
    });
    
    if (result.success) {
      return NextResponse.json(result.data, { status: 200 });
    } else {
      // Determine appropriate status code based on error
      let statusCode = 500;
      
      if (result.error?.includes('Invalid email or password') || 
          result.error?.includes('Unauthorized')) {
        statusCode = 401;
      } else if (result.error?.includes('Please verify your email')) {
        statusCode = 403;
      } else if (result.error?.includes('Validation error')) {
        statusCode = 422;
      }
      
      return NextResponse.json({
        error: result.error,
      }, { status: statusCode });
    }
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors,
      }, { status: 422 });
    }
    
    console.error('Error during login:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}