import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getOTP, deleteOTP, getUser, updateUser } from '@/lib/storage';
import { isOTPExpired } from '@/lib/otp-utils';

// Validation schema for OTP verification
const verifyOTPSchema = z.object({
  email: z.string().email('Invalid email format'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Request body is required',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = verifyOTPSchema.parse(body);
    const { email, otp } = validatedData;

    // Check if developer exists
    const developer = getUser(email);
    
    if (!developer) {
      return NextResponse.json({
        success: false,
        message: 'Developer not found',
      }, { status: 404 });
    }

    // Check if account is already verified
    if (developer.isVerified && developer.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Account already verified',
      }, { status: 409 });
    }

    // Get stored OTP
    const storedOTPData = getOTP(email);
    
    if (!storedOTPData) {
      return NextResponse.json({
        success: false,
        message: 'OTP not found or expired. Please request a new OTP.',
      }, { status: 400 });
    }

    // Check if OTP has expired
    if (isOTPExpired(storedOTPData.expiry)) {
      deleteOTP(email);
      return NextResponse.json({
        success: false,
        message: 'OTP expired',
      }, { status: 400 });
    }

    // Verify OTP
    if (storedOTPData.otp !== otp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP',
      }, { status: 400 });
    }

    // OTP is valid, activate the developer account
    const updatedDeveloper = updateUser(email, {
      isVerified: true,
      isActive: true,
    });

    // Clean up OTP
    deleteOTP(email);

    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully',
    }, { status: 200 });

  } catch (error) {
    console.error('Error in OTP verification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
