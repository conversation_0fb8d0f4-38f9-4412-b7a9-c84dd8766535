import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getUser } from '@/lib/storage';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Validation schema for developer login
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

// JWT secret for token generation
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Request body is required',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = loginSchema.parse(body);
    const { email, password } = validatedData;

    // Check if developer exists
    const developer = getUser(email);
    
    if (!developer) {
      return NextResponse.json({
        success: false,
        message: 'Developer not found',
      }, { status: 404 });
    }

    // Check if developer is verified
    if (!developer.isVerified || !developer.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Please verify your email before logging in',
      }, { status: 400 });
    }

    // Check password
    if (!developer.password) {
      return NextResponse.json({
        success: false,
        message: 'Invalid credentials',
      }, { status: 400 });
    }

    const isPasswordValid = await bcrypt.compare(password, developer.password);
    
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        message: 'Invalid credentials',
      }, { status: 400 });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: developer.id,
        email: developer.email,
        socialType: developer.socialType || 'email',
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Return success response (matching Swagger spec)
    return NextResponse.json({
      token,
      data: {
        id: developer.id,
        name: developer.name || '',
        email: developer.email,
        isActive: developer.isActive,
        createdAt: developer.createdAt,
      },
    }, { status: 200 });

  } catch (error) {
    console.error('Error in developer login:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
