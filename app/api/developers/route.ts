import { NextRequest, NextResponse } from 'next/server';
import { getUser, deleteUser } from '@/lib/storage';
import jwt from 'jsonwebtoken';

// JWT secret for token verification
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

export async function DELETE(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        message: 'Authorization header required',
      }, { status: 401 });
    }

    const token = authHeader.substring(7);

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid or expired token',
      }, { status: 401 });
    }

    // Get developer from storage
    const developer = getUser(decoded.email);
    
    if (!developer) {
      return NextResponse.json({
        success: false,
        message: 'Developer not found',
      }, { status: 404 });
    }

    // Delete developer account
    const deleted = deleteUser(decoded.email);
    
    if (!deleted) {
      return NextResponse.json({
        success: false,
        message: 'Failed to delete developer account',
      }, { status: 500 });
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Developer deleted successfully',
    }, { status: 200 });

  } catch (error) {
    console.error('Error deleting developer:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
