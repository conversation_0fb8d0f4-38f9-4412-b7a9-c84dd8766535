"use client"

import React, { useEffect, useState, useCallback } from "react"
import { useAuth } from "@/lib/auth-context"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"
import { apiService } from "@/lib/api"
import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper"

export default function ProfilePage() {
  const { user, token, refreshProfile } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState<any>(null)
  const [applications, setApplications] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  
  // Fetch profile data and applications
  const fetchProfileData = useCallback(async () => {
    if (!token) return

    try {
      setIsLoading(true)
      setError(null)

      // Fetch profile data
      const profileResponse = await apiService.getProfile(token)
      if (profileResponse.success && profileResponse.data) {
        setProfileData(profileResponse.data)
      } else {
        setError(profileResponse.error || "Failed to fetch profile data")
      }

      // Fetch applications for activity data
      const appsResponse = await apiService.getApplications(token)
      if (appsResponse.success && appsResponse.data) {
        setApplications(appsResponse.data)
      }
    } catch (error) {
      setError("Failed to fetch profile data")
      console.error('Error fetching profile:', error)
    } finally {
      setIsLoading(false)
    }
  }, [token])

  // Redirect if not authenticated
  useEffect(() => {
    if (!user || !token) {
      router.push("/auth/signin")
    } else {
      fetchProfileData()
    }
  }, [user, token, router, fetchProfileData])
  
  // Get initials for avatar
  const getInitials = (email: string) => {
    return email.split('@')[0].substring(0, 1).toUpperCase()
  }

  // Format date to be more readable
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }
  
  return (
    <DashboardLayoutWrapper title="Profile">
      <div className="max-w-6xl mx-auto">
        {isLoading ? (
          <div className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-xl p-8 sm:p-12 flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-[#4A148C] mb-4"></div>
            <p className="text-[#4A148C] font-medium">Loading profile...</p>
          </div>
        ) : error ? (
          <div className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-xl p-8 sm:p-12 flex flex-col items-center justify-center">
            <p className="text-red-600 font-semibold">{error}</p>
            <Button
              onClick={fetchProfileData}
              className="mt-4 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
            >
              Try Again
            </Button>
          </div>
        ) : (

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* User overview card */}
        <Card className="p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
          <div className="flex flex-col items-center text-center">
            <Avatar className="h-16 w-16 sm:h-20 sm:w-20 lg:h-24 lg:w-24 border-4 border-[#B497D6]/30 shadow-lg">
              <AvatarFallback className="bg-gradient-to-br from-[#4A148C] to-[#7B1FA2] text-white font-bold text-xl sm:text-2xl lg:text-3xl">
                {profileData?.email ? getInitials(profileData.email) : "U"}
              </AvatarFallback>
            </Avatar>

            <h2 className="mt-4 text-lg sm:text-xl font-semibold text-[#4A148C]">
              {profileData?.name || profileData?.email?.split('@')[0] || "User"}
            </h2>
            <p className="text-xs sm:text-sm text-gray-500 mt-1 truncate max-w-full">{profileData?.email}</p>

            <div className="mt-3 px-3 py-1 bg-gradient-to-r from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-full text-[#4A148C] font-medium text-sm border border-[#B497D6]/20">
              Developer
            </div>

            <div className="mt-6 w-full space-y-3">
              <div className="flex justify-between text-sm p-2 bg-[#B497D6]/5 rounded-lg">
                <span className="text-gray-600 font-medium">Account ID:</span>
                <span className="font-mono text-xs text-[#4A148C] font-semibold">{profileData?.id || "N/A"}</span>
              </div>
              <div className="flex justify-between text-sm p-2 bg-[#B497D6]/5 rounded-lg">
                <span className="text-gray-600 font-medium">Created:</span>
                <span className="font-medium text-[#4A148C]">{formatDate(profileData?.createdAt)}</span>
              </div>
              <div className="flex justify-between text-sm p-2 bg-[#B497D6]/5 rounded-lg">
                <span className="text-gray-600 font-medium">Status:</span>
                <span className={`font-semibold ${profileData?.isActive ? 'text-green-600' : 'text-amber-600'}`}>
                  {profileData?.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* User details card */}
        <Card className="p-6 col-span-1 md:col-span-2 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Account Details</h2>
          </div>

          <div className="space-y-6">
            <div>
              <Label htmlFor="name" className="text-[#4A148C] font-medium">Full Name</Label>
              <Input
                id="name"
                value={profileData?.name || ""}
                disabled
                className="mt-2 bg-[#B497D6]/5 border-[#B497D6]/20 text-[#4A148C] font-medium"
              />
            </div>

            <div>
              <Label htmlFor="email" className="text-[#4A148C] font-medium">Email Address</Label>
              <Input
                id="email"
                value={profileData?.email || ""}
                disabled
                className="mt-2 bg-[#B497D6]/5 border-[#B497D6]/20 text-[#4A148C] font-medium"
              />
            </div>

            <div>
              <Label htmlFor="id" className="text-[#4A148C] font-medium">Developer ID</Label>
              <Input
                id="id"
                value={profileData?.id || ""}
                disabled
                className="mt-2 bg-[#B497D6]/5 border-[#B497D6]/20 font-mono text-sm text-[#4A148C] font-medium"
              />
            </div>

            <div>
              <Label htmlFor="verification" className="text-[#4A148C] font-medium">Account Status</Label>
              <div className="mt-2 flex items-center p-3 bg-[#B497D6]/5 rounded-lg border border-[#B497D6]/20">
                <div className={`h-3 w-3 rounded-full mr-3 ${profileData?.isActive ? "bg-green-500 shadow-lg shadow-green-500/30" : "bg-amber-500 shadow-lg shadow-amber-500/30"}`}></div>
                <span className={`font-semibold ${profileData?.isActive ? 'text-green-600' : 'text-amber-600'}`}>
                  {profileData?.isActive ? "Active" : "Inactive"}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Activity and usage stats card */}
        <Card className="p-6 col-span-1 md:col-span-3 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <h2 className="text-xl font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-6">Account Activity</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gradient-to-br from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-xl border border-[#B497D6]/20 hover:shadow-lg transition-all duration-300">
              <h3 className="text-sm font-medium text-gray-600">Account Created</h3>
              <p className="text-lg font-semibold mt-1 text-[#4A148C]">{formatDate(profileData?.createdAt)}</p>
            </div>

            <div className="p-4 bg-gradient-to-br from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-xl border border-[#B497D6]/20 hover:shadow-lg transition-all duration-300">
              <h3 className="text-sm font-medium text-gray-600">Total Applications</h3>
              <p className="text-lg font-semibold mt-1 text-[#4A148C]">{applications.length}</p>
            </div>

            <div className="p-4 bg-gradient-to-br from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-xl border border-[#B497D6]/20 hover:shadow-lg transition-all duration-300">
              <h3 className="text-sm font-medium text-gray-600">Account Status</h3>
              <p className={`text-lg font-semibold mt-1 ${profileData?.isActive ? 'text-green-600' : 'text-amber-600'}`}>
                {profileData?.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <Button
              variant="outline"
              onClick={() => router.push("/dashboard/dashboard/api-keys")}
              className="border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
            >
              Manage API Keys
            </Button>
          </div>
        </Card>
      </div>
          )}
        </div>
    </DashboardLayoutWrapper>
  )
}
