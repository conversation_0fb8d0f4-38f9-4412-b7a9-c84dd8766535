'use client';

import { WalletConnect, useWalletConnection } from '@/components/shared/client-wallet-connect';

export default function TestWalletPage() {
  const { isConnected, address } = useWalletConnection();

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Wallet Test Page</h1>
      
      <div className="space-y-4">
        <div>
          <strong>Connection Status:</strong> {isConnected ? 'Connected' : 'Not Connected'}
        </div>
        
        {address && (
          <div>
            <strong>Address:</strong> {address}
          </div>
        )}
        
        <div>
          <WalletConnect />
        </div>
      </div>
    </div>
  );
}
