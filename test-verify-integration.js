// Simple test script to verify the app verification integration
// Run with: node test-verify-integration.js

const axios = require('axios');

const API_BASE_URL = 'https://mutually-factual-weevil.ngrok-free.app/api/v1';

async function testVerifyIntegration() {
  console.log('Testing App Verification Integration...\n');

  // Test data based on the API documentation
  const testData = {
    appId: "app_5f8d04a7b4b3c9001f3e5a9b",
    apiKey: "ak_5f8d04a7b4b3c9001f3e5a9b"
  };

  try {
    console.log('1. Testing direct backend API call...');
    const directResponse = await axios.post(`${API_BASE_URL}/apps/verify`, testData, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    console.log('✅ Direct API call successful');
    console.log('Response:', JSON.stringify(directResponse.data, null, 2));
    
  } catch (error) {
    console.log('❌ Direct API call failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }

  try {
    console.log('\n2. Testing through Next.js API route...');
    const nextResponse = await axios.post('http://localhost:3000/api/apps/verify', testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Next.js API route call successful');
    console.log('Response:', JSON.stringify(nextResponse.data, null, 2));
    
  } catch (error) {
    console.log('❌ Next.js API route call failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }

  console.log('\n3. Testing with invalid credentials...');
  const invalidData = {
    appId: "invalid_app_id",
    apiKey: "invalid_api_key"
  };

  try {
    const invalidResponse = await axios.post(`${API_BASE_URL}/apps/verify`, invalidData, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    console.log('Response:', JSON.stringify(invalidResponse.data, null, 2));
    
  } catch (error) {
    console.log('✅ Invalid credentials properly rejected');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    }
  }
}

testVerifyIntegration().catch(console.error);