// Simple test script to verify backend connection
const axios = require('axios');

const API_BASE_URL = 'https://mutually-factual-weevil.ngrok-free.app/api/v1';

async function testBackendConnection() {
  try {
    console.log('Testing backend connection...');
    console.log('API Base URL:', API_BASE_URL);
    
    // Test basic connectivity
    const response = await axios.get(`${API_BASE_URL}/health`, {
      headers: {
        'ngrok-skip-browser-warning': 'true',
      },
      timeout: 10000,
    });
    
    console.log('✅ Backend connection successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
  } catch (error) {
    console.log('❌ Backend connection failed!');
    
    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', error.response.data);
      } else if (error.request) {
        console.log('No response received from server');
        console.log('Request details:', error.request);
      } else {
        console.log('Error setting up request:', error.message);
      }
    } else {
      console.log('Unexpected error:', error);
    }
  }
}

// Test applications endpoint (without auth for now)
async function testAppsEndpoint() {
  try {
    console.log('\nTesting /apps endpoint...');
    
    const response = await axios.get(`${API_BASE_URL}/apps`, {
      headers: {
        'ngrok-skip-browser-warning': 'true',
        'Authorization': 'Bearer test-token', // This will likely fail but shows the endpoint structure
      },
      timeout: 10000,
    });
    
    console.log('✅ Apps endpoint accessible!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
  } catch (error) {
    console.log('❌ Apps endpoint test failed (expected if no auth)');
    
    if (axios.isAxiosError(error) && error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
      
      // 401 is expected without proper auth
      if (error.response.status === 401) {
        console.log('✅ Endpoint exists but requires authentication (as expected)');
      }
    }
  }
}

// Run tests
async function runTests() {
  await testBackendConnection();
  await testAppsEndpoint();
}

runTests();