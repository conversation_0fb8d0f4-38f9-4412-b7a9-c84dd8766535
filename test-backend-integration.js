#!/usr/bin/env node

const API_BASE_URL = 'https://crefy-connect-backend-7ph6.onrender.com/api/v1';

async function testBackendIntegration() {
  console.log('🚀 Testing Backend Integration...\n');

  // Test 1: Health Check
  console.log('1. Testing Health Check...');
  try {
    const response = await fetch(`${API_BASE_URL.replace('/api/v1', '')}/health`);
    const data = await response.json();
    console.log('✅ Health Check:', data);
  } catch (error) {
    console.log('❌ Health Check Failed:', error.message);
  }

  // Test 2: Test Auth Registration (with a test email)
  console.log('\n2. Testing Auth Registration...');
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123'
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Registration Response:', data);
    } else {
      const errorData = await response.json();
      console.log('⚠️ Registration Response (expected if user exists):', response.status, errorData);
    }
  } catch (error) {
    console.log('❌ Registration Failed:', error.message);
  }

  // Test 3: Test Apps Endpoint (without auth - should fail)
  console.log('\n3. Testing Apps Endpoint (without auth)...');
  try {
    const response = await fetch(`${API_BASE_URL}/apps`);
    const data = await response.json();
    
    if (response.status === 401) {
      console.log('✅ Apps Endpoint correctly requires authentication:', data);
    } else {
      console.log('⚠️ Unexpected response:', response.status, data);
    }
  } catch (error) {
    console.log('❌ Apps Endpoint Test Failed:', error.message);
  }

  console.log('\n🎉 Backend Integration Test Complete!');
  console.log('\nNext Steps:');
  console.log('1. The backend is accessible and responding');
  console.log('2. Authentication endpoints are working');
  console.log('3. Apps endpoints require proper authentication');
  console.log('4. Frontend is now configured to use the real backend');
}

testBackendIntegration().catch(console.error);