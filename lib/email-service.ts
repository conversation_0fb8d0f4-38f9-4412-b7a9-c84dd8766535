import nodemailer from 'nodemailer';

// Email configuration
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
};

// Validate email configuration
if (!process.env.SMTP_USER || !process.env.SMTP_PASS || 
    process.env.SMTP_USER === '<EMAIL>' || 
    process.env.SMTP_PASS === 'your-app-password') {
  console.warn('⚠️  Email service not configured properly. Please set up SMTP credentials in .env.local');
  console.warn('📧 For Gmail:');
  console.warn('   1. Enable 2-Factor Authentication on your Google account');
  console.warn('   2. Generate an App Password: https://myaccount.google.com/apppasswords');
  console.warn('   3. Update SMTP_USER with your Gmail address');
  console.warn('   4. Update SMTP_PASS with the generated App Password');
}

// Create transporter
const transporter = nodemailer.createTransport(EMAIL_CONFIG);

export async function sendOTPEmail(email: string, otp: string): Promise<void> {
  try {
    // Check if email is properly configured
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS || 
        process.env.SMTP_USER === '<EMAIL>' || 
        process.env.SMTP_PASS === 'your-app-password') {
      console.log(`⚠️  Email service not configured. OTP for ${email}: ${otp}`);
      console.log('📧 Please configure SMTP credentials in .env.local to send actual emails');
      return; // Skip sending email but don't throw error
    }

    // Verify transporter configuration
    await transporter.verify();

    const mailOptions = {
      from: {
        name: 'Crefy Connect',
        address: process.env.SMTP_FROM || process.env.SMTP_USER || '<EMAIL>',
      },
      to: email,
      subject: 'Your Crefy Connect Verification Code',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verification Code</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              padding: 20px 0;
              border-bottom: 2px solid #4B0082;
            }
            .logo {
              font-size: 24px;
              font-weight: bold;
              background: linear-gradient(135deg, #4B0082, #B497D6);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
            .content {
              padding: 30px 0;
              text-align: center;
            }
            .otp-code {
              font-size: 32px;
              font-weight: bold;
              color: #4B0082;
              background: #f8f9fa;
              padding: 20px;
              border-radius: 8px;
              letter-spacing: 4px;
              margin: 20px 0;
              border: 2px dashed #B497D6;
            }
            .footer {
              text-align: center;
              padding: 20px 0;
              border-top: 1px solid #eee;
              color: #666;
              font-size: 14px;
            }
            .warning {
              background: #fff3cd;
              border: 1px solid #ffeaa7;
              color: #856404;
              padding: 15px;
              border-radius: 5px;
              margin: 20px 0;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">Crefy Connect</div>
          </div>
          
          <div class="content">
            <h1>Verify Your Email Address</h1>
            <p>Thank you for signing up for Crefy Connect! To complete your registration, please use the verification code below:</p>
            
            <div class="otp-code">${otp}</div>
            
            <p>This code will expire in <strong>10 minutes</strong>.</p>
            
            <div class="warning">
              <strong>Security Notice:</strong> Never share this code with anyone. Crefy Connect will never ask for your verification code via phone or email.
            </div>
            
            <p>If you didn't request this verification code, please ignore this email.</p>
          </div>
          
          <div class="footer">
            <p>This email was sent by Crefy Connect</p>
            <p>If you have any questions, please contact our support team.</p>
          </div>
        </body>
        </html>
      `,
      text: `
        Crefy Connect - Email Verification
        
        Thank you for signing up for Crefy Connect!
        
        Your verification code is: ${otp}
        
        This code will expire in 10 minutes.
        
        If you didn't request this verification code, please ignore this email.
        
        Best regards,
        The Crefy Connect Team
      `,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('OTP email sent successfully:', info.messageId);
    
  } catch (error) {
    console.error('Failed to send OTP email:', error);
    throw new Error('Failed to send verification email');
  }
}

// Test email configuration
export async function testEmailConfiguration(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log('Email configuration is valid');
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
}