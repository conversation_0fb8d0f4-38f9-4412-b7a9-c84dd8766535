// Mock database for development purposes
interface User {
  id: string;
  email: string;
  password: string;
  isVerified: boolean;
  createdAt: string;
  role?: 'admin' | 'user';
}

interface App {
  id: string;
  name: string;
  description: string;
  apiKey: string;
  userId: string;
  createdAt: string;
  isActive: boolean;
}

interface Credential {
  id: string;
  type: string;
  data: Record<string, unknown>;
  userId: string;
  createdAt: string;
  isVerified: boolean;
}

class MockDatabase {
  private usersList: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      password: 'admin123',
      isVerified: true,
      createdAt: new Date().toISOString(),
      role: 'admin'
    },
    {
      id: '2',
      email: '<EMAIL>',
      password: 'user123',
      isVerified: true,
      createdAt: new Date().toISOString(),
      role: 'user'
    }
  ];

  private appsList: App[] = [
    {
      id: '1',
      name: 'Sample App',
      description: 'A sample application',
      apiKey: 'sk_test_123456789',
      userId: '1',
      createdAt: new Date().toISOString(),
      isActive: true
    }
  ];

  private credentialsList: Credential[] = [
    {
      id: '1',
      type: 'identity',
      data: { name: '<PERSON> Doe', age: 30 },
      userId: '1',
      createdAt: new Date().toISOString(),
      isVerified: true
    }
  ];

  // User methods
  users = {
    findByEmail: (email: string): User | undefined => {
      return this.usersList.find(user => user.email === email);
    },
    
    findById: (id: string): User | undefined => {
      return this.usersList.find(user => user.id === id);
    },
    
    create: (userData: Omit<User, 'id' | 'createdAt'>): User => {
      const newUser: User = {
        ...userData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      this.usersList.push(newUser);
      return newUser;
    },
    
    update: (id: string, updates: Partial<User>): User | undefined => {
      const userIndex = this.usersList.findIndex(user => user.id === id);
      if (userIndex === -1) return undefined;
      
      this.usersList[userIndex] = { ...this.usersList[userIndex], ...updates };
      return this.usersList[userIndex];
    },
    
    getAll: (): User[] => {
      return this.usersList;
    }
  };

  // App methods
  apps = {
    findByUserId: (userId: string): App[] => {
      return this.appsList.filter(app => app.userId === userId);
    },
    
    findById: (id: string): App | undefined => {
      return this.appsList.find(app => app.id === id);
    },
    
    create: (appData: Omit<App, 'id' | 'createdAt'>): App => {
      const newApp: App = {
        ...appData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      this.appsList.push(newApp);
      return newApp;
    },
    
    update: (id: string, updates: Partial<App>): App | undefined => {
      const appIndex = this.appsList.findIndex(app => app.id === id);
      if (appIndex === -1) return undefined;
      
      this.appsList[appIndex] = { ...this.appsList[appIndex], ...updates };
      return this.appsList[appIndex];
    },
    
    delete: (id: string): boolean => {
      const appIndex = this.appsList.findIndex(app => app.id === id);
      if (appIndex === -1) return false;
      
      this.appsList.splice(appIndex, 1);
      return true;
    }
  };

  // Credential methods
  credentials = {
    findByUserId: (userId: string): Credential[] => {
      return this.credentialsList.filter(credential => credential.userId === userId);
    },
    
    findById: (id: string): Credential | undefined => {
      return this.credentialsList.find(credential => credential.id === id);
    },
    
    create: (credentialData: Omit<Credential, 'id' | 'createdAt'>): Credential => {
      const newCredential: Credential = {
        ...credentialData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      this.credentialsList.push(newCredential);
      return newCredential;
    },
    
    update: (id: string, updates: Partial<Credential>): Credential | undefined => {
      const credentialIndex = this.credentialsList.findIndex(credential => credential.id === id);
      if (credentialIndex === -1) return undefined;
      
      this.credentialsList[credentialIndex] = { ...this.credentialsList[credentialIndex], ...updates };
      return this.credentialsList[credentialIndex];
    },
    
    delete: (id: string): boolean => {
      const credentialIndex = this.credentialsList.findIndex(credential => credential.id === id);
      if (credentialIndex === -1) return false;
      
      this.credentialsList.splice(credentialIndex, 1);
      return true;
    }
  };
}

export const mockDb = new MockDatabase();